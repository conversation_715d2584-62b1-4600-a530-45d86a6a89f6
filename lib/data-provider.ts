
import { ApolloClient, InMemoryCache } from '@apollo/client';
import buildHasuraProvider from 'ra-data-hasura';
import { DataProvider, GetListParams, GetOneParams, UpdateParams } from 'react-admin';
import { isEqual } from 'lodash';


const dataProvider: DataProvider = buildHasuraProvider({
  client: new ApolloClient({
    uri: process.env.NEXT_PUBLIC_HASURA_URI,
    cache: new InMemoryCache(),
    headers: {
      'x-hasura-admin-secret': process.env.NEXT_PUBLIC_HASURA_ADMIN_SECRET || "",
    },
  })
})

const customDataProvider: DataProvider = {
  ...dataProvider,
  getList: async (resource: string, params: GetListParams) => {
    return await dataProvider.getList(resource, params);
  },
  getOne: async (resource: string, params: GetOneParams) => {

    if (resource === 'health_product_variants') {
      try {

        const productVariant = await dataProvider.getOne(resource, params);

        const queryParams: GetListParams = {
          filter: { health_variant_id: params.id },
          pagination: { page: 1, perPage: 50 },
          sort: { field: 'id', order: 'ASC' }
        }

        const faqsResponse = await dataProvider.getList('site_health_variant_faq', queryParams);
        const whyOneAssureResponse = await dataProvider.getList('site_health_variant_whyoneassure', queryParams);
        const healthVariantStaticContentResponse = await dataProvider.getList('site_health_variant_static_content', queryParams);
        const healthVariantFeaturesResponse = await dataProvider.getList('site_health_variant_features', queryParams);
        const healthVariantRelatedVariantsResponse = await dataProvider.getList('site_health_variant_related_variants', queryParams);
        const healthVariantNetworkHospitalsResponse = await dataProvider.getList('site_health_variant_network_hospitals', queryParams);
        const healthVariantAddonsResponse = await dataProvider.getList('site_health_variant_addons', queryParams);
        const healthVariantRatingsResponse = await dataProvider.getList('site_health_variant_ratings', queryParams);
        // console.log(healthVariantStaticContentResponse.data[0]);
        // console.log(faqsResponse);

        const data = {
          ...productVariant.data,
          health_variant_faqs: faqsResponse.data,
          health_variant_whyoneassures: whyOneAssureResponse.data,
          health_variant_static_content: healthVariantStaticContentResponse.data[0],
          health_variant_features: healthVariantFeaturesResponse.data,
          health_variant_related_variants: healthVariantRelatedVariantsResponse.data,
          health_variant_network_hospitals: healthVariantNetworkHospitalsResponse.data[0],
          health_variant_addons: healthVariantAddonsResponse.data,
          health_variant_ratings: healthVariantRatingsResponse.data
        }

        // console.log(data);
        return {
          data: data
        };
      } catch (e) {
        console.error('Error fetching product variant with FAQs:', e);
        return dataProvider.getOne(resource, params);
      }
    }
    return dataProvider.getOne(resource, params);
  },

  update: async (resource: string, params: UpdateParams) => {

    if (resource === 'health_product_variants') {
      try {

        const { health_variant_faqs, 
          health_variant_whyoneassures, 
          health_variant_static_content, 
          health_variant_features,
          health_variant_related_variants,
          health_variant_network_hospitals, 
          health_variant_addons,
          health_variant_ratings,
          ...productVariantData } = params.data;

        // console.log(params)

        const updatedVariant = await dataProvider.update(resource, {
          id: params.id,
          data: productVariantData,
          previousData: {
            data: params.previousData
          }
        });

        if (health_variant_faqs && Array.isArray(health_variant_faqs)) {
          const existingFaqsResponse = await dataProvider.getList('site_health_variant_faq', {
            filter: { health_variant_id: params.id },
            pagination: { page: 1, perPage: 50 },
            sort: { field: 'id', order: 'ASC' }
          });

          const existingFaqIds = existingFaqsResponse.data.map(faq => faq.id);
          const updatedFaqIds = health_variant_faqs.filter(faq => faq.id).map(faq => faq.id);
          const faqsToDelete = existingFaqIds.filter(id => !updatedFaqIds.includes(id));

          for (const faqId of faqsToDelete) {
            await dataProvider.delete('site_health_variant_faq', { id: faqId });
          }

          for (const faq of health_variant_faqs) {
            if (!faq.question && !faq.answer) continue;

            const faqData = {
              question: faq.question || '',
              answer: faq.answer || '',
              health_variant_id: params.id
            };

            if (faq.id && existingFaqIds.includes(faq.id)) {
              await dataProvider.update('site_health_variant_faq', {
                id: faq.id,
                data: faqData,
                previousData: existingFaqsResponse.data.find(f => f.id === faq.id)
              });
            } else {
              await dataProvider.create('site_health_variant_faq', {
                data: { ...faqData, id: generatePseudoRandomKey(16) }
              });
            }
          }
        }

        if (health_variant_whyoneassures && Array.isArray(health_variant_whyoneassures)) {
          const existingWhyOneAssuresResponse = await dataProvider.getList('site_health_variant_whyoneassure', {
            filter: { health_variant_id: params.id },
            pagination: { page: 1, perPage: 50 },
            sort: { field: 'id', order: 'ASC' }
          });

          const existingWhyOneAssureIds = existingWhyOneAssuresResponse.data.map(whyOneAssure => whyOneAssure.id);
          const updatedWhyOneAssureIds = health_variant_whyoneassures.filter(whyOneAssure => whyOneAssure.id).map(whyOneAssure => whyOneAssure.id);
          const whyOneAssuresToDelete = existingWhyOneAssureIds.filter(id => !updatedWhyOneAssureIds.includes(id));

          for (const whyOneAssureId of whyOneAssuresToDelete) {
            await dataProvider.delete('site_health_variant_whyoneassure', { id: whyOneAssureId });
          }

          for (const whyOneAssure of health_variant_whyoneassures) {
            if (!whyOneAssure.title && !whyOneAssure.description) continue;

            const whyOneAssureData = {
              title: whyOneAssure.title || '',
              description: whyOneAssure.description || '',
              health_variant_id: params.id
            };

            if (whyOneAssure.id && existingWhyOneAssureIds.includes(whyOneAssure.id)) {
              await dataProvider.update('site_health_variant_whyoneassure', {
                id: whyOneAssure.id,
                data: whyOneAssureData,
                previousData: existingWhyOneAssuresResponse.data.find(f => f.id === whyOneAssure.id)
              });
            } else {
              await dataProvider.create('site_health_variant_whyoneassure', {
                data: { ...whyOneAssureData, id: generatePseudoRandomKey(16) }
              });
            }
          }
        }

        if (health_variant_static_content) {
          const existingContentList = await dataProvider.getList(
            'site_health_variant_static_content',
            {
              filter: { health_variant_id: params.id },
              pagination: { page: 1, perPage: 1 },
              sort: { field: 'id', order: 'ASC' }
            }
          );

          if (existingContentList.data.length > 0) {
            const existingContent = existingContentList.data[0];

            if (!isEqual(existingContent, { ...existingContent, ...health_variant_static_content })) {
              await dataProvider.update('site_health_variant_static_content', {
                id: existingContent.id,
                data: {
                  ...health_variant_static_content
                },
                previousData: existingContent
              });
            } else {
              console.log('No changes detected — skipping update');
            }
          } else {
            await dataProvider.create('site_health_variant_static_content', {
              data: {
                id: generatePseudoRandomKey(16),
                health_variant_id: params.id,
                ...health_variant_static_content
              }
            });
          }
        }


        ////////////////// FEATURES ////////////////

        if (health_variant_features && Array.isArray(health_variant_features)) {
          const existingFeaturesResponse = await dataProvider.getList('site_health_variant_features', {
            filter: { health_variant_id: params.id },
            pagination: { page: 1, perPage: 1000 },
            sort: { field: 'id', order: 'ASC' }
          });
          // console.log(health_variant_features);
          // console.log(existingFeaturesResponse);

          const existingFeatureIds = existingFeaturesResponse.data.map(feature => feature.id);
          const updatedFeatureIds = health_variant_features.filter(feature => feature.id).map(feature => feature.id);
          const featuresToDelete = existingFeatureIds.filter(id => !updatedFeatureIds.includes(id));

          for (const featureId of featuresToDelete) {
            await dataProvider.delete('site_health_variant_features', { id: featureId });
          }

          for (const feature of health_variant_features) {
            // console.log(feature);
            if (
              !feature.title &&
              !feature.description &&
              (!feature.listed_features || feature.listed_features.length === 0)
            ) {
              continue;
            }

            const featureData = {
              title: feature.title || '',
              description: feature.description || '',
              listed_features: feature.listed_features || [],
              health_variant_id: params.id
            };
            console.log(featureData);

            if (feature.id && existingFeatureIds.includes(feature.id)) {
              await dataProvider.update('site_health_variant_features', {
                id: feature.id,
                data: featureData,
                previousData: existingFeaturesResponse.data.find(f => f.id === feature.id)
              });
            } else {
              await dataProvider.create('site_health_variant_features', {
                data: { ...featureData, id: generatePseudoRandomKey(16) }
              });
            }
          }
        }
        ///////////////// 

        ///////////////// RELATED VARIANTS ////////////////

        if (health_variant_related_variants && Array.isArray(health_variant_related_variants)) {
          const existingRelatedVariantsResponse = await dataProvider.getList('site_health_variant_related_variants', {
            filter: { health_variant_id: params.id },
            pagination: { page: 1, perPage: 1000 },
            sort: { field: 'id', order: 'ASC' }
          });

          const existingRelatedVariantIds = existingRelatedVariantsResponse.data.map(relatedVariant => relatedVariant.id);
          const updatedRelatedVariantIds = health_variant_related_variants.filter(relatedVariant => relatedVariant.id).map(relatedVariant => relatedVariant.id);
          const relatedVariantsToDelete = existingRelatedVariantIds.filter(id => !updatedRelatedVariantIds.includes(id));

          for (const relatedVariantId of relatedVariantsToDelete) {
            await dataProvider.delete('site_health_variant_related_variants', { id: relatedVariantId });
          }

          for (const relatedVariant of health_variant_related_variants) {
            if (!relatedVariant.related_variant_id) continue;

            const relatedVariantData = {
              related_variant_id: relatedVariant.related_variant_id,
              health_variant_id: params.id,
              features: relatedVariant.features || []
            };

            if (relatedVariant.id && existingRelatedVariantIds.includes(relatedVariant.id)) {
              await dataProvider.update('site_health_variant_related_variants', {
                id: relatedVariant.id,
                data: relatedVariantData,
                previousData: existingRelatedVariantsResponse.data.find(f => f.id === relatedVariant.id)
              });
            } else {
              await dataProvider.create('site_health_variant_related_variants', {
                data: { ...relatedVariantData, id: generatePseudoRandomKey(16) }
              });
            }
          }
        }

        ///////////////// NETWORK HOSPITALS ////////////////

        if (health_variant_network_hospitals) {
          const existingNetworkHospitalsResponse = await dataProvider.getList('site_health_variant_network_hospitals', {
            filter: { health_variant_id: params.id },
            pagination: { page: 1, perPage: 1000 },
            sort: { field: 'id', order: 'ASC' }
          });

          if (existingNetworkHospitalsResponse.data.length > 0) {
            const existingNetworkHospitals = existingNetworkHospitalsResponse.data[0];

            if (!isEqual(existingNetworkHospitals, { ...existingNetworkHospitals, ...health_variant_network_hospitals })) {
              await dataProvider.update('site_health_variant_network_hospitals', {
                id: existingNetworkHospitals.id,
                data: {
                  ...health_variant_network_hospitals
                },
                previousData: existingNetworkHospitals
              });
            } else {
              console.log('No changes detected — skipping update');
            }
          } else {
            await dataProvider.create('site_health_variant_network_hospitals', {
              data: {
                id: generatePseudoRandomKey(16),
                health_variant_id: params.id,
                ...health_variant_network_hospitals
              }
            });
          }
        }

        ///////////////// ADDONS ////////////////

        if (health_variant_addons && Array.isArray(health_variant_addons)) {
          const existingAddonsResponse = await dataProvider.getList('site_health_variant_addons', {
            filter: { health_variant_id: params.id },
            pagination: { page: 1, perPage: 1000 },
            sort: { field: 'id', order: 'ASC' }
          });

          const existingAddonsIds = existingAddonsResponse.data.map(addon => addon.id);
          const updatedAddonsIds = health_variant_addons.filter(addon => addon.id).map(addon => addon.id);
          const addonsToDelete = existingAddonsIds.filter(id => !updatedAddonsIds.includes(id));

          for (const addonId of addonsToDelete) {
            await dataProvider.delete('site_health_variant_addons', { id: addonId });
          }

          for (const addon of health_variant_addons) {
            if (!addon.title && !addon.description) continue;

            const addonData = {
              title: addon.title || '',
              description: addon.description || '',
              health_variant_id: params.id
            };

            if (addon.id && existingAddonsIds.includes(addon.id)) {
              await dataProvider.update('site_health_variant_addons', {
                id: addon.id,
                data: addonData,
                previousData: existingAddonsResponse.data.find(f => f.id === addon.id)
              });
            } else {
              await dataProvider.create('site_health_variant_addons', {
                data: { ...addonData, id: generatePseudoRandomKey(16) }
              });
            }
          }
        }

        ///////////////// RATINGS ////////////////

        if (health_variant_ratings && Array.isArray(health_variant_ratings)) {
          const existingRatingsResponse = await dataProvider.getList('site_health_variant_ratings', {
            filter: { health_variant_id: params.id },
            pagination: { page: 1, perPage: 1000 },
            sort: { field: 'id', order: 'ASC' }
          });

          const existingRatingsIds = existingRatingsResponse.data.map(rating => rating.id);
          const updatedRatingsIds = health_variant_ratings.filter(rating => rating.id).map(rating => rating.id);
          const ratingsToDelete = existingRatingsIds.filter(id => !updatedRatingsIds.includes(id));

          for (const ratingId of ratingsToDelete) {
            await dataProvider.delete('site_health_variant_ratings', { id: ratingId });
          }

          for (const rating of health_variant_ratings) {
            if (!rating.label || !rating.title || !rating.score || !rating.max_score) continue;

            const ratingData = {
              label: rating.label || '',
              title: rating.title || '',
              score: rating.score || 0,
              max_score: rating.max_score || 0,
              health_variant_id: params.id
            };

            if (rating.id && existingRatingsIds.includes(rating.id)) {
              await dataProvider.update('site_health_variant_ratings', {
                id: rating.id,
                data: ratingData,
                previousData: existingRatingsResponse.data.find(f => f.id === rating.id)
              });
            } else {
              console.log(ratingData);
              await dataProvider.create('site_health_variant_ratings', {
                data: { ...ratingData, id: generatePseudoRandomKey(16) }
              });
            }
          }
        }


        return updatedVariant;

      } catch (error) {
        console.error('Error updating product variant with FAQs:', error);
        throw error;
      }
    }

    return dataProvider.update(resource, params);
  }
}

function generatePseudoRandomKey(length: number) {
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}

export default customDataProvider;