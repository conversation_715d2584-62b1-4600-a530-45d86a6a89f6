// components/Health_productList.tsx
'use client';
import { <PERSON><PERSON>an<PERSON>ield, Create, Datagrid, DataTable, DateField, Edit, EditButton, Filter, List, ListButton, ReferenceField, ReferenceInput, SimpleForm, TextField, TextInput, TopToolbar, useRecordContext } from 'react-admin';

const ProductFilter = (props: any)=>{
    return (<Filter {...props}>
        <TextInput label="search by name" source='name' alwaysOn={true}></TextInput>
        {/* <TextInput label="insurer" source="insurer_id"/> */}
    </Filter>)
}

const ProductTitle = ()=>{
    const record = useRecordContext();
    console.log(typeof record);
    return (<span>{record? record.name: ''}</span>)
}

const ProductEditActions = ({ resource}: { resource: string}) => (
    <TopToolbar>
        <ListButton resource={resource} label="Back" />
    </TopToolbar>
);


export const Health_productEdit = () => (
    <Edit title={<ProductTitle></ProductTitle>} actions={<ProductEditActions resource="health_products"></ProductEditActions>}>
        <SimpleForm>
            <TextInput source="name" />
            {/* <TextInput source="insurer_id" /> */}
            <ReferenceInput source='insurer_id' reference="health_insurers"></ReferenceInput>
            <TextInput source="plan_type" />
            <TextInput source="policy_brochure_url" />
            <TextInput source="policy_wording_url" />
        </SimpleForm>
    </Edit>
);


export const Health_productCreate = () => (
    <Create>
        <SimpleForm>
            <TextInput source="name" />
            <TextInput source="insurer_id" />
            <TextInput source="plan_type" />
            <TextInput source="policy_brochure_url" />
            <TextInput source="policy_wording_url" />
        </SimpleForm>
    </Create>
);


export const Health_productList = () => (
    <List filters={<ProductFilter></ProductFilter>}>
        <Datagrid rowClick="edit">
            <TextField source="id" />
            <TextField source="name" />
            <ReferenceField source="insurer_id" reference="health_insurers" />
            <TextField source="plan_type" />
            <TextField source="policy_brochure_url" />
            <TextField source="policy_wording_url" />
            <EditButton></EditButton>
        </Datagrid>
    </List>
);

