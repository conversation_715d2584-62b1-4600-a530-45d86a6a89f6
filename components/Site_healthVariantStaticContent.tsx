"use client";

import { ArrayInput, AutocompleteInput, BooleanField, DataTable, DateField, Edit, EditButton, List, ListButton, ReferenceField, ReferenceInput, SelectArrayInput, SelectInput, SimpleForm, TextField, TextInput, TopToolbar } from 'react-admin';



const ProductEditActions = ({ resource }: { resource: string }) => (
    <TopToolbar>
        <ListButton resource={resource} label="Back" />
    </TopToolbar>
);

export const Site_healthVariantStaticContentEdit = () => (
    <Edit title={"Edit Content"} actions={<ProductEditActions resource="site_health_variant_static_content"></ProductEditActions>}>
        <SimpleForm>
            <ReferenceInput source='health_variant_id' reference="health_product_variants" label="Variant">
                <SelectInput optionText={"variant_name"}></SelectInput>
            </ReferenceInput>

            <ReferenceInput source='product_id' reference="health_products" label="Product">
                <SelectInput optionText={"name"} />
            </ReferenceInput>
        </SimpleForm>
    </Edit>
);

export const Site_health_variant_static_contentList = () => (
    <List>
        <DataTable>
            <DataTable.Col label="insurer">
                <ReferenceField source='health_variant_id' reference="health_product_variants">
                    <ReferenceField source="product_id" reference="health_products">
                        <ReferenceField source="insurer_id" reference="health_insurers">
                            <TextField source="name" />
                        </ReferenceField>
                    </ReferenceField>
                </ReferenceField>
            </DataTable.Col>

            <DataTable.Col label="product">
                <ReferenceField source='health_variant_id' reference="health_product_variants">
                    <ReferenceField source="product_id" reference="health_products">
                        <TextField source="name" />
                    </ReferenceField>
                </ReferenceField>
            </DataTable.Col>

            <DataTable.Col source="health_variant_id" label="Variant">
                <ReferenceField source="health_variant_id" reference="health_product_variants">
                    <TextField source="variant_name" />
                </ReferenceField>
            </DataTable.Col>

            <DataTable.Col source="updated_at">
                <DateField source="updated_at" />
            </DataTable.Col>
        </DataTable>

        <EditButton></EditButton>
    </List>
);