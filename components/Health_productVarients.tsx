"use client";

import { RichTextInput } from 'ra-input-rich-text';
import { ArrayInput, DataTable, Edit, List, ListButton, NumberInput, ReferenceField, ReferenceInput, SelectInput, SimpleForm, SimpleFormIterator, TextInput, TopToolbar } from 'react-admin';


const VariantEditActions = () => {
    return (<TopToolbar>
        <ListButton resource="health_product_variants" label="Back" />
    </TopToolbar>)
}

export const Health_product_variantEdit = () => (
    <Edit actions={<VariantEditActions></VariantEditActions>}>
        <SimpleForm>

            <ReferenceInput source='product_id' reference="health_products" label="Product">
                <SelectInput optionText={"name"} />
            </ReferenceInput>
            <TextInput source="variant_name" />
            <TextInput source="variant_slug" />

            {/* FAQ SECTION */}

            <ArrayInput source='health_variant_faqs' label="FAQs">
                <SimpleFormIterator>
                    <TextInput source="id" disabled style={{ display: 'none' }} />
                    <TextInput source="created_at" disabled style={{ display: 'none' }} />
                    <TextInput source="updated_at" disabled style={{ display: 'none' }} />
                    <TextInput source='question'></TextInput>
                    <RichTextInput source='answer'></RichTextInput>
                </SimpleFormIterator>
            </ArrayInput>

            {/* WHY ONEASSURE SECTION */}

            <ArrayInput source='health_variant_whyoneassures' label="Why OneAssure">
                <SimpleFormIterator>
                    <TextInput source="id" disabled style={{ display: 'none' }} />
                    <TextInput source="created_at" disabled style={{ display: 'none' }} />
                    <TextInput source="updated_at" disabled style={{ display: 'none' }} />
                    <TextInput source='title'></TextInput>
                    <RichTextInput source='description'></RichTextInput>
                </SimpleFormIterator>
            </ArrayInput>

            {/* EXCLUSIONS SECTION */}

            <ArrayInput source="health_variant_static_content.exclusions" label="Exclusions">
                <SimpleFormIterator>
                    <TextInput source="" label="Exclusion" />
                </SimpleFormIterator>
            </ArrayInput>

            {/* ABOUT THE PLAN */}

            <RichTextInput source="health_variant_static_content.about_the_plan" label="About the Plan"></RichTextInput>

            {/* HEALTH VARIANT FEATURES */}

            <ArrayInput source="health_variant_features" label="Features">
                <SimpleFormIterator>
                    <TextInput source="title" label="Title" />
                    <RichTextInput source="description" label="Description" />
                    <ArrayInput source='listed_features' label="Listed Features">
                        <SimpleFormIterator>
                            <RichTextInput source="" label="Feature" />
                        </SimpleFormIterator>
                    </ArrayInput>
                </SimpleFormIterator>
            </ArrayInput>

            {/* RATINGS */}
            <ArrayInput source='health_variant_ratings' label="Ratings">
                <SimpleFormIterator>
                    <SelectInput source="label" label="Rating Type" choices={[
                        { id: 'coverage', name: 'Coverage' },
                        { id: 'claimSettlement', name: 'Claim Settlement' },
                        { id: 'hospitalNetwork', name: 'Hospital Network' },
                        { id: 'coPayment', name: 'Co-Payment' },
                        { id: 'waitingPeriods', name: 'Waiting Periods' },
                        { id: 'noClaimBonus', name: 'No Claim Bonus' }
                    ]} />
                    <TextInput source="title" label="Title" />
                    <NumberInput source="score" label="Score" />
                    <NumberInput source="max_score" label="Max Score" />
                </SimpleFormIterator>
            </ArrayInput>

            {/* RELATED VARIANTS */}

            <ArrayInput source='health_variant_related_variants' label="Related Variants">
                <SimpleFormIterator>
                    <ReferenceInput
                        source='related_variant_id'
                        reference="health_product_variants"
                        label="Related Variant"
                    >
                        <SelectInput optionText="variant_name" />
                    </ReferenceInput>

                    <ArrayInput source='features' label="Features">
                        <SimpleFormIterator>
                            <TextInput source="" label="Feature" />
                        </SimpleFormIterator>
                    </ArrayInput>
                </SimpleFormIterator>
            </ArrayInput>


            {/* HERO SECTION */}

            <TextInput source="health_variant_static_content.hero_title" label="Hero Title" />
            <NumberInput source='health_variant_static_content.claim_settlement_ratio' label="Claim Settlement Ratio" />
            <TextInput source='health_variant_network_hospitals.value' label="Network Hospitals Value"/>
            <TextInput source="health_variant_network_hospitals.description" label="Network Hospitals Description" />
            <TextInput source='health_variant_network_hospitals.network_hospital_url' label="Network Hospitals URL" />

            {/* ADDONS */}

            <ArrayInput source='health_variant_addons' label="Addons">
                <SimpleFormIterator>
                    <TextInput source="title" label="title" />
                    <RichTextInput source="description" label="Description" />
                </SimpleFormIterator>
            </ArrayInput>
            
        </SimpleForm>
    </Edit>
);



export const Health_product_variantList = () => (
    <List>
        <DataTable>
            <DataTable.Col source="product_id">
                <ReferenceField source="product_id" reference="health_products" />
            </DataTable.Col>
            <DataTable.Col source="variant_name" />
            <DataTable.Col source="variant_slug" />
        </DataTable>
    </List>
);